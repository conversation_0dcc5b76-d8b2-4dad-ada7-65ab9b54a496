{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/omi_health/android/app/.cxx/Debug/4k1r4j56/armeabi-v7a", "source": "/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}