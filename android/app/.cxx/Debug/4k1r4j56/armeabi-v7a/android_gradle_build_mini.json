{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/omi_health/android/app/.cxx/Debug/4k1r4j56/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/omi_health/android/app/.cxx/Debug/4k1r4j56/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}